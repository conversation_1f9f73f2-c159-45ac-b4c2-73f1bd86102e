# MOSCI Delete Button Mant Field Validation Implementation

## Summary
Implemented delete button validation based on the `Mant` field value in the MOSCI grid functionality. The delete button is now enabled (red) only when an offender ID exists with `Mant = 'CHG'`, and disabled (gray) otherwise.

## Changes Made

### 1. Backend Changes

#### MosciRepository.cs
- **Location:** Lines 38-74
- **Change:** Added `Mant` field retrieval from the database in `GetMosciInfoByOaksId` method
- **Details:** Added try-catch block to safely retrieve the MANT field from the stored procedure result

```csharp
// Add Mant field retrieval with try-catch for safety
try
{
    int mantOrdinal = reader.GetOrdinal("MANT");
    mosciData.Mant = reader.IsDBNull(mantOrdinal) ? "" : reader.GetString(mantOrdinal).Trim();
}
catch (IndexOutOfRangeException)
{
    // MANT field doesn't exist in the result set, set to empty string
    mosciData.Mant = "";
}
```

#### MosciController.cs
- **Location:** Lines 682-747
- **Change:** Added new `CheckDeleteEligibility` method
- **Details:** 
  - Validates if offender ID exists and has `Mant = 'CHG'`
  - Returns JSON response indicating whether delete button should be enabled
  - Handles error cases gracefully

```csharp
[HttpPost]
public JsonResult CheckDeleteEligibility(string prefix, string offenderId)
{
    // Implementation validates Mant field and returns eligibility status
}
```

### 2. Frontend Changes

#### Index.cshtml JavaScript
- **Location:** Lines 906-964, 996-1023, 627-631, 692-714, 1012-1025
- **Changes:** 
  1. Added `checkDeleteEligibility()` function to call backend validation
  2. Added `updateDeleteButtonState()` function to control button appearance
  3. Added flag `deleteButtonControlledByMant` to prevent conflicts with existing logic
  4. Modified existing `updateButtonState()` function to respect Mant validation
  5. Added blur event handler for offender ID fields
  6. Updated auto-populate success callback to trigger delete eligibility check
  7. Updated field clearing logic to reset Mant validation control

## Functionality Flow

1. **User enters Offender ID** → Auto-populate triggers
2. **Auto-populate completes** → `checkDeleteEligibility()` called
3. **AJAX validation** → Checks if Mant = 'CHG' in database
4. **Button state updated** → Enabled (red) if eligible, disabled (gray) if not
5. **User clicks delete** → Confirmation modal shown
6. **User confirms** → Individual deletion performed
7. **Success** → Row cleared, button disabled

## Validation Rules

- **Enable Delete Button When:**
  - Offender ID exists in MOSCI records
  - Mant field equals 'CHG' (case-insensitive)
  - All required data present (prefix, offender ID, schedule date)

- **Disable Delete Button When:**
  - Offender ID doesn't exist
  - Mant field is not 'CHG' or is empty
  - No offender ID entered
  - Error occurs during validation

## Event Triggers

- **On offender ID change** (auto-populate)
- **On offender ID blur** (when user finishes entering ID)
- **On field clearing** (resets validation state)

## Technical Features

- **Conflict Prevention:** Uses `deleteButtonControlledByMant` flag to prevent existing button logic from overriding Mant validation
- **Template Row Support:** Works with both existing grid rows and newly added template rows
- **Error Handling:** Graceful handling of database errors and missing fields
- **Console Logging:** Detailed logging for debugging and monitoring

## Testing Instructions

### Test Case 1: Valid Offender with Mant = 'CHG'
1. Enter a valid prefix (e.g., 'A')
2. Enter an offender ID that exists in MOSCI with Mant = 'CHG'
3. **Expected:** Delete button becomes red and enabled
4. **Verify:** Button tooltip shows "Delete selected inmates - Mant field is CHG"

### Test Case 2: Valid Offender with Mant ≠ 'CHG'
1. Enter a valid prefix (e.g., 'A')
2. Enter an offender ID that exists in MOSCI with Mant ≠ 'CHG'
3. **Expected:** Delete button remains gray and disabled
4. **Verify:** Button tooltip shows "Delete button disabled - Mant field is not CHG"

### Test Case 3: Non-existent Offender
1. Enter a valid prefix (e.g., 'A')
2. Enter an offender ID that doesn't exist in MOSCI
3. **Expected:** Delete button remains gray and disabled
4. **Verify:** Button tooltip shows appropriate message

### Test Case 4: Empty Offender ID
1. Clear the offender ID field
2. **Expected:** Delete button becomes gray and disabled
3. **Verify:** Mant validation control is reset

### Test Case 5: Template Row Functionality
1. Click "Add New Inmate" to create a new row
2. Enter offender ID in the new row
3. **Expected:** Same validation behavior as existing rows

## Browser Console Monitoring

Monitor browser console for these log messages:
- "Checking delete eligibility for: [prefix][offenderId]"
- "Delete button enabled (red) - Mant field is CHG"
- "Delete button disabled (gray) - Mant field is not CHG"
- "Delete button state controlled by Mant field validation - skipping default update"

## Error Handling

The implementation includes comprehensive error handling:
- Database connection errors
- Missing Mant field in stored procedure results
- Invalid offender ID formats
- AJAX request failures

All errors are logged to console and result in disabled delete button state for safety.
